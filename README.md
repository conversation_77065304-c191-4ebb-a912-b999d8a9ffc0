# PDF/SVG to PNG Converter

A comprehensive Python GUI application that converts PDF and SVG files to PNG images with LaTeX integration features.

## Features

### Core Conversion Features
- **Folder Selection**: Browse and select a folder containing PDF and/or SVG files
- **Batch Conversion**: Converts all selected file types in the folder
- **Quality Control**: Adjustable DPI settings (72-300)
- **Flexible Options**: Convert all pages or just the first page of each PDF
- **Progress Tracking**: Real-time progress bar and conversion log
- **File Naming**: Preserves original filename, changes extension to .png
- **Multi-page Support**: For multi-page PDFs, creates separate PNG files (e.g., `document_page_1.png`, `document_page_2.png`)

### SVG Support
- **SVG Conversion**: Optional conversion of SVG files to PNG format
- **High Quality**: Uses svglib and reportlab for professional-quality conversion

### LaTeX Integration
- **LaTeX File Processing**: Optional processing of .tex files to update references
- **Smart PDF Reference Updates**: Only updates PDF references when PDF conversion is enabled
- **Smart SVG Reference Conversion**: Only converts SVG references when SVG conversion is enabled
- **LaTeX-Generated PDF Exclusion**: Automatically ignores PDF files generated from the selected LaTeX file
- **Backup Creation**: Automatically creates backup files before making changes

## Requirements

- Python 3.7 or higher
- PyMuPDF (fitz) - for PDF processing
- Pillow (PIL) - for image handling
- svglib - for SVG conversion
- reportlab - for graphics rendering

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Run the application:
```bash
python pdf_to_png_converter.py
```

2. **Select Folders**:
   - **Main Folder**: Click "Browse" to select the primary folder containing files
   - **Figure Folder** (Optional): Click "Browse" to select an additional figure folder

3. **Configure Conversion Options** (Select at least one):
   - **Convert PDF files to PNG**: Enable PDF conversion
   - **Convert SVG files to PNG**: Enable SVG conversion
   - **Convert first page only (PDF)**: Only convert the first page of multi-page PDFs
   - **DPI**: Higher values = better quality but larger file sizes (default: 150)

4. **Configure LaTeX Processing** (Optional):
   - Click "Browse .tex" to select a LaTeX file for processing
   - **Update .pdf to .png references**: Changes `.pdf` to `.png` in `\includegraphics{}` commands (only if PDF conversion is enabled)
   - **Convert \\includesvg to \\includegraphics**: Changes `\includesvg{}` to `\includegraphics{}` and updates to `.png` (only if SVG conversion is enabled)

5. Click "Start Conversion" to begin the process

6. Monitor progress in the log area

## Output

### File Conversion
- PNG files are saved in the same folder as the original files
- **PDF files**:
  - Single-page PDFs: `filename.png`
  - Multi-page PDFs: `filename_page_1.png`, `filename_page_2.png`, etc.
  - If "First page only" is selected: `filename.png` (regardless of page count)
- **SVG files**: `filename.png`

### LaTeX Processing
- **Backup files**: Original `.tex` file is backed up as `filename.tex.backup`
- **Reference updates**: LaTeX file is updated with new PNG references
- **Pattern matching**:
  - `\includegraphics{file.pdf}` → `\includegraphics{file.png}`
  - `\includesvg{file}` → `\includegraphics{file.png}`
  - `\includesvg{file.svg}` → `\includegraphics{file.png}`

## Important Features

- **Selective Conversion**: Choose PDF, SVG, or both file types for conversion
- **LaTeX-Generated PDF Exclusion**: Automatically ignores PDF files that match the LaTeX filename (e.g., if you select `document.tex`, it will ignore `document.pdf`)
- **Multi-Folder Support**: Process files from both main folder and optional figure folder
- **Smart LaTeX Processing**: Only processes LaTeX references for file types that are being converted
- **Validation**: Requires at least one conversion type to be selected before starting

## Technical Notes

- **PDF Processing**: Uses PyMuPDF for excellent quality and performance
- **SVG Processing**: Uses svglib and reportlab for professional-quality conversion
- **Threading**: Conversion runs in a separate thread to keep the GUI responsive
- **Cancellation**: You can cancel the conversion process at any time
- **Logging**: Detailed information about each file and operation is shown in the log
- **Safety**: LaTeX files are automatically backed up before modification
