# PDF/SVG to PNG Converter

A comprehensive Python GUI application that converts PDF and SVG files to PNG images with LaTeX integration features.

## Features

### Core Conversion Features
- **Folder Selection**: Browse and select a folder containing PDF and/or SVG files
- **Batch Conversion**: Converts all selected file types in the folder
- **Quality Control**: Adjustable DPI settings (72-300)
- **Flexible Options**: Convert all pages or just the first page of each PDF
- **Progress Tracking**: Real-time progress bar and conversion log
- **File Naming**: Preserves original filename, changes extension to .png
- **Multi-page Support**: For multi-page PDFs, creates separate PNG files (e.g., `document_page_1.png`, `document_page_2.png`)

### SVG Support
- **SVG Conversion**: Optional conversion of SVG files to PNG format
- **High Quality**: Uses svglib and reportlab for professional-quality conversion

### LaTeX Integration
- **LaTeX File Processing**: Optional processing of .tex files to update references
- **PDF Reference Updates**: Automatically changes PDF references to PNG in LaTeX files
- **SVG Reference Conversion**: Converts `\includesvg{}` commands to `\includegraphics{}` and updates file extensions
- **Backup Creation**: Automatically creates backup files before making changes

## Requirements

- Python 3.7 or higher
- PyMuPDF (fitz) - for PDF processing
- Pillow (PIL) - for image handling
- svglib - for SVG conversion
- reportlab - for graphics rendering

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Run the application:
```bash
python pdf_to_png_converter.py
```

2. **Select Files**: Click "Browse" to select a folder containing PDF and/or SVG files

3. **Configure Conversion Options**:
   - **DPI**: Higher values = better quality but larger file sizes (default: 150)
   - **First page only**: Check to convert only the first page of each PDF
   - **Also convert SVG files**: Check to include SVG files in the conversion

4. **Configure LaTeX Processing** (Optional):
   - Click "Browse .tex" to select a LaTeX file for processing
   - **Update PDF references**: Automatically changes `.pdf` to `.png` in `\includegraphics{}` commands
   - **Convert SVG references**: Changes `\includesvg{}` to `\includegraphics{}` and updates file extensions

5. Click "Start Conversion" to begin the process

6. Monitor progress in the log area

## Output

### File Conversion
- PNG files are saved in the same folder as the original files
- **PDF files**:
  - Single-page PDFs: `filename.png`
  - Multi-page PDFs: `filename_page_1.png`, `filename_page_2.png`, etc.
  - If "First page only" is selected: `filename.png` (regardless of page count)
- **SVG files**: `filename.png`

### LaTeX Processing
- **Backup files**: Original `.tex` file is backed up as `filename.tex.backup`
- **Reference updates**: LaTeX file is updated with new PNG references
- **Pattern matching**:
  - `\includegraphics{file.pdf}` → `\includegraphics{file.png}`
  - `\includesvg{file}` → `\includegraphics{file.png}`
  - `\includesvg{file.svg}` → `\includegraphics{file.png}`

## Notes

- **PDF Processing**: Uses PyMuPDF for excellent quality and performance
- **SVG Processing**: Uses svglib and reportlab for professional-quality conversion
- **Threading**: Conversion runs in a separate thread to keep the GUI responsive
- **Cancellation**: You can cancel the conversion process at any time
- **Logging**: Detailed information about each file and operation is shown in the log
- **Safety**: LaTeX files are automatically backed up before modification
