import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import threading
from pathlib import Path
import fitz  # PyMuPDF
from PIL import Image
import io
import re
from svglib.svglib import svg2rlg
from reportlab.graphics import renderPM


class PDFToPNGConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF/SVG to PNG Converter")
        self.root.geometry("700x600")
        self.root.resizable(True, True)

        # Variables
        self.selected_folder = tk.StringVar()
        self.selected_tex_file = tk.StringVar()
        self.conversion_running = False

        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="PDF/SVG to PNG Converter",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Folder selection
        ttk.Label(main_frame, text="Select Folder:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        folder_entry = ttk.Entry(main_frame, textvariable=self.selected_folder, 
                                state="readonly", width=50)
        folder_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 10), pady=5)
        
        browse_button = ttk.Button(main_frame, text="Browse", command=self.browse_folder)
        browse_button.grid(row=1, column=2, pady=5)
        
        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Conversion Options", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        options_frame.columnconfigure(1, weight=1)
        
        # DPI setting
        ttk.Label(options_frame, text="DPI (Quality):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.dpi_var = tk.StringVar(value="150")
        dpi_spinbox = ttk.Spinbox(options_frame, from_=72, to=300, width=10, 
                                 textvariable=self.dpi_var)
        dpi_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Convert first page only option
        self.first_page_only = tk.BooleanVar(value=False)
        first_page_check = ttk.Checkbutton(options_frame, text="Convert first page only",
                                          variable=self.first_page_only)
        first_page_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)

        # SVG conversion option
        self.convert_svg = tk.BooleanVar(value=False)
        svg_check = ttk.Checkbutton(options_frame, text="Also convert SVG files to PNG",
                                   variable=self.convert_svg)
        svg_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        # LaTeX processing options
        latex_frame = ttk.LabelFrame(main_frame, text="LaTeX File Processing (Optional)", padding="10")
        latex_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        latex_frame.columnconfigure(1, weight=1)

        # LaTeX file selection
        ttk.Label(latex_frame, text="LaTeX File:").grid(row=0, column=0, sticky=tk.W, pady=5)

        tex_entry = ttk.Entry(latex_frame, textvariable=self.selected_tex_file,
                             state="readonly", width=40)
        tex_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10), pady=5)

        browse_tex_button = ttk.Button(latex_frame, text="Browse .tex", command=self.browse_tex_file)
        browse_tex_button.grid(row=0, column=2, pady=5)

        # LaTeX processing options
        self.update_pdf_refs = tk.BooleanVar(value=False)
        pdf_refs_check = ttk.Checkbutton(latex_frame, text="Update PDF references to PNG in .tex file",
                                        variable=self.update_pdf_refs)
        pdf_refs_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=5)

        self.update_svg_refs = tk.BooleanVar(value=False)
        svg_refs_check = ttk.Checkbutton(latex_frame, text="Convert \\includesvg to \\includegraphics and update SVG to PNG",
                                        variable=self.update_svg_refs)
        svg_refs_check.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # Progress frame
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        progress_frame.columnconfigure(0, weight=1)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # Status label
        self.status_label = ttk.Label(progress_frame, text="Ready to convert")
        self.status_label.grid(row=1, column=0, pady=5)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        # Convert button
        self.convert_button = ttk.Button(buttons_frame, text="Start Conversion",
                                        command=self.start_conversion)
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Cancel button
        self.cancel_button = ttk.Button(buttons_frame, text="Cancel", 
                                       command=self.cancel_conversion, state="disabled")
        self.cancel_button.pack(side=tk.LEFT)
        
        # Log text area
        log_frame = ttk.LabelFrame(main_frame, text="Conversion Log", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=20)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        # Text widget with scrollbar
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
    def browse_folder(self):
        folder = filedialog.askdirectory(title="Select folder containing PDF/SVG files")
        if folder:
            self.selected_folder.set(folder)
            self.log_message(f"Selected folder: {folder}")

    def browse_tex_file(self):
        tex_file = filedialog.askopenfilename(
            title="Select LaTeX file",
            filetypes=[("LaTeX files", "*.tex"), ("All files", "*.*")]
        )
        if tex_file:
            self.selected_tex_file.set(tex_file)
            self.log_message(f"Selected LaTeX file: {tex_file}")
            
    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def start_conversion(self):
        if not self.selected_folder.get():
            messagebox.showerror("Error", "Please select a folder first!")
            return
            
        # Start conversion in a separate thread
        self.conversion_running = True
        self.convert_button.config(state="disabled")
        self.cancel_button.config(state="normal")
        
        thread = threading.Thread(target=self.convert_pdfs)
        thread.daemon = True
        thread.start()
        
    def cancel_conversion(self):
        self.conversion_running = False
        self.convert_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        self.status_label.config(text="Conversion cancelled")
        self.log_message("Conversion cancelled by user")
        
    def convert_pdfs(self):
        try:
            folder_path = Path(self.selected_folder.get())

            # Collect files to convert
            files_to_convert = []

            # Add PDF files
            pdf_files = list(folder_path.glob("*.pdf"))
            files_to_convert.extend([(f, 'pdf') for f in pdf_files])

            # Add SVG files if option is enabled
            if self.convert_svg.get():
                svg_files = list(folder_path.glob("*.svg"))
                files_to_convert.extend([(f, 'svg') for f in svg_files])

            if not files_to_convert:
                file_types = "PDF" + (" and SVG" if self.convert_svg.get() else "")
                self.log_message(f"No {file_types} files found in the selected folder")
                self.conversion_complete()
                return

            self.log_message(f"Found {len(files_to_convert)} file(s) to convert")

            dpi = int(self.dpi_var.get())
            first_page_only = self.first_page_only.get()

            for i, (file_path, file_type) in enumerate(files_to_convert):
                if not self.conversion_running:
                    break

                self.status_label.config(text=f"Converting: {file_path.name}")
                self.log_message(f"Converting: {file_path.name}")

                try:
                    if file_type == 'pdf':
                        self.convert_single_pdf(file_path, dpi, first_page_only)
                    elif file_type == 'svg':
                        self.convert_single_svg(file_path, dpi)
                    self.log_message(f"✓ Successfully converted: {file_path.name}")
                except Exception as e:
                    self.log_message(f"✗ Error converting {file_path.name}: {str(e)}")

                # Update progress
                progress = ((i + 1) / len(files_to_convert)) * 50  # 50% for file conversion
                self.progress_var.set(progress)

            # Process LaTeX file if options are enabled
            if self.conversion_running and self.selected_tex_file.get():
                if self.update_pdf_refs.get() or self.update_svg_refs.get():
                    self.status_label.config(text="Processing LaTeX file...")
                    self.log_message("Processing LaTeX file...")
                    try:
                        self.process_latex_file()
                        self.log_message("✓ LaTeX file processed successfully")
                    except Exception as e:
                        self.log_message(f"✗ Error processing LaTeX file: {str(e)}")

            if self.conversion_running:
                self.log_message("All tasks completed!")
                self.status_label.config(text="All tasks completed")

            self.progress_var.set(100)

        except Exception as e:
            self.log_message(f"Error during conversion: {str(e)}")
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            self.conversion_complete()
            
    def convert_single_pdf(self, pdf_path, dpi, first_page_only):
        # Open PDF
        doc = fitz.open(pdf_path)
        
        try:
            pages_to_convert = [0] if first_page_only else range(len(doc))
            
            for page_num in pages_to_convert:
                if not self.conversion_running:
                    break
                    
                page = doc.load_page(page_num)
                
                # Create transformation matrix for DPI
                mat = fitz.Matrix(dpi/72, dpi/72)
                
                # Render page to pixmap
                pix = page.get_pixmap(matrix=mat)
                
                # Convert to PIL Image
                img_data = pix.tobytes("png")
                img = Image.open(io.BytesIO(img_data))
                
                # Generate output filename
                if len(doc) == 1 or first_page_only:
                    output_name = pdf_path.stem + ".png"
                else:
                    output_name = f"{pdf_path.stem}_page_{page_num + 1}.png"
                
                output_path = pdf_path.parent / output_name
                
                # Save PNG
                img.save(output_path, "PNG")
                
        finally:
            doc.close()

    def convert_single_svg(self, svg_path, dpi):
        """Convert a single SVG file to PNG"""
        output_name = svg_path.stem + ".png"
        output_path = svg_path.parent / output_name

        # Convert SVG to PNG using svglib and reportlab
        drawing = svg2rlg(str(svg_path))
        renderPM.drawToFile(drawing, str(output_path), fmt='PNG', dpi=dpi)

    def process_latex_file(self):
        """Process the LaTeX file to update references"""
        tex_file_path = Path(self.selected_tex_file.get())

        if not tex_file_path.exists():
            raise FileNotFoundError(f"LaTeX file not found: {tex_file_path}")

        # Read the LaTeX file
        with open(tex_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Update PDF references to PNG if option is enabled
        if self.update_pdf_refs.get():
            # Pattern to match \includegraphics{filename.pdf}
            pdf_pattern = r'(\\includegraphics(?:\[[^\]]*\])?\{[^}]*?)\.pdf(\})'
            content = re.sub(pdf_pattern, r'\1.png\2', content)
            self.log_message("Updated PDF references to PNG in LaTeX file")

        # Update SVG references if option is enabled
        if self.update_svg_refs.get():
            # Pattern to match \includesvg{filename} or \includesvg{filename.svg}
            svg_pattern = r'\\includesvg(?:\[[^\]]*\])?\{([^}]*?)(?:\.svg)?\}'

            def replace_svg(match):
                filename = match.group(1)
                return f'\\includegraphics{{{filename}.png}}'

            content = re.sub(svg_pattern, replace_svg, content)
            self.log_message("Converted \\includesvg to \\includegraphics and updated SVG to PNG references")

        # Write back to file if changes were made
        if content != original_content:
            # Create backup
            backup_path = tex_file_path.with_suffix('.tex.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            self.log_message(f"Created backup: {backup_path.name}")

            # Write updated content
            with open(tex_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.log_message(f"Updated LaTeX file: {tex_file_path.name}")
        else:
            self.log_message("No changes needed in LaTeX file")

    def conversion_complete(self):
        self.conversion_running = False
        self.convert_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        self.progress_var.set(0)


def main():
    root = tk.Tk()
    app = PDFToPNGConverter(root)
    root.mainloop()


if __name__ == "__main__":
    main()
